from django.contrib import admin
from datetime import datetime

from django.utils.html import format_html
from django.utils.safestring import mark_safe
from django.template.loader import render_to_string

from core.models import Product


# Register your models here.

@admin.register(Product)
class ProductAdmin(admin.ModelAdmin):
    model_fields = [
        'product_title',
        'product_price',
        'commission_ratio',
        'service_ratio',
        'commission_amount',
        'service_amount',
        'stock_num',
        'product_status',
        'shop_name',
        'product_id',
        'spu_id',
        'promotion_start_time',
        'promotion_end_time',
        'source_name',
        'item_link',
    ]

    list_display = [
        'product_image_display',
        'product_title',
        'spu_id',
        'promotion_link_display',
        'product_price',
        'commission_display',
        'service_fee_display',
        'promotion_time_display_admin',
        'status_display_admin',
        'shop_name',
        'stock_num',
        'promotion_restriction_status_display',
        'guarantee_display',
    ]

    list_filter = [
        'product_status',
        'is_hidden',
        'shop_name',
        'source_name',
    ]

    search_fields = [
        'product_title',
        'product_id',
        'spu_id',
        'shop_name',
    ]

    fields = ['product_data'] + model_fields
    readonly_fields = model_fields

    @admin.display(
        description='商品图片',
        empty_value='无图片'
    )
    def product_image_display(self, obj):
        """显示商品图片"""
        if obj.product_img_url:
            return format_html(
                '<img src="{}" width="50" height="50" style="object-fit: cover;" />',
                obj.product_img_url,
            )
        return None

    @admin.display(
        description='达人佣金',
        ordering='commission_amount'
    )
    def commission_display(self, obj):
        """显示佣金信息"""
        return f"{obj.commission_amount}元\n({obj.commission_ratio}%)"

    @admin.display(
        description='我的服务费',
        ordering='service_amount'
    )
    def service_fee_display(self, obj):
        """显示服务费信息"""
        return f"{obj.service_amount}元\n({obj.service_ratio}%)"

    @admin.display(
        description='推广时间',
        ordering='promotion_end_time'
    )
    def promotion_time_display_admin(self, obj):
        """显示推广时间"""
        if obj.promotion_end_time == 4294967290:  # 永久推广的特殊值
            return '永久'

        try:
            start_time = datetime.fromtimestamp(obj.promotion_start_time).strftime('%Y-%m-%d %H:%M')
            end_time = datetime.fromtimestamp(obj.promotion_end_time).strftime('%Y-%m-%d %H:%M')
            return f'{start_time} - {end_time}'
        except Exception:
            return None

    @admin.display(
        description='状态',
        ordering='product_status'
    )
    def status_display_admin(self, obj):
        """显示商品状态"""
        # 商品状态显示
        status_map = {
            1: '推广中',
            0: '暂停推广',
        }
        status = status_map.get(obj.product_status, '未知')

        is_hidden_display = '已隐藏' if obj.is_hidden else ''
        is_hidden = obj.is_hidden
        if status == '推广中':
            status_color = 'green'
        elif status == '暂停推广':
            status_color = 'red'
        else:
            status_color = 'orange'

        return format_html(
            '<p style="color: {};">{}</p>'
            '<p display="{}">{}</p>',
            status_color,
            status,
            is_hidden,
            is_hidden_display,
        )

    @admin.display(description='推广限制')
    def promotion_restriction_status_display(self, obj):
        """显示推广限制状态，鼠标悬停显示原因"""
        # 是否有推广限制
        item_info = obj.product_data.get('itemInfo', {})
        result = item_info.get('result', {})
        admission = result.get('admission', True)

        # 获取所有限制项目
        restriction_items = []
        try:
            restriction_list = result.get('list', [])
            for item in restriction_list:
                restriction_items.append({
                    'title': item.get('title', '限制项目'),
                    'wording': item.get('wording', '无详细说明'),
                    'admission': item.get('admission', False)
                })
        except:
            pass

        context = {
            'has_restriction': not admission,
            'restriction_items': restriction_items,
        }
        html = render_to_string(
            'core/admin_widgets/promotion_restriction_status_display.html',
            context,
        )
        return mark_safe(html)

    @admin.display(
        description='商品保障',
        ordering='product_data'
    )
    def guarantee_display(self, obj):
        """显示商品保障标识"""
        # 商品保障标识列表
        product_info = obj.product_data.get('productInfo', {})
        guarantee_flags = product_info.get('guaranteeFlag', [])
        flags = [flag.get('guarantee', '') for flag in guarantee_flags]
        return ' | '.join(flags) if flags else None

    @admin.display(description='推广链接')
    def promotion_link_display(self, obj):
        """显示推广链接按钮，点击弹出推广链接和二维码"""
        if not obj.item_link or not obj.qrcode_link:
            return None

        context = {
            'obj': obj,
        }
        html = render_to_string(
            'core/admin_widgets/promotion_link_display.html',
            context,
        )
        return mark_safe(html)

    def get_queryset(self, request):
        """优化查询"""
        return super().get_queryset(request).select_related()
